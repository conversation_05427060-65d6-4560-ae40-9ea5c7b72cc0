import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/components/app_back_button.dart';
import '../../../core/constants/constants.dart';
import '../../../core/components/app_settings_tile.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/profile/settings/language_settings_page.dart';
import '../../../views/profile/settings/notifications_settings_page.dart';
import '../../../views/profile/settings/change_password_page.dart';
import '../../../views/profile/settings/change_phone_number_page.dart';
import '../../../views/profile/address/address_page.dart';
import '../../../views/profile/profile_edit_page.dart';
import '../../../views/auth/intro_login_page.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text(
          'Settings',
        ),
      ),
      backgroundColor: AppColors.cardColor,
      body: Container(
        margin: const EdgeInsets.all(AppDefaults.padding),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDefaults.padding,
          vertical: AppDefaults.padding * 2,
        ),
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
          borderRadius: AppDefaults.borderRadius,
        ),
        child: Column(
          children: [
            AppSettingsListTile(
              label: 'Language',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () =>
                  nav.Router.push(context, const LanguageSettingsPage()),
            ),
            AppSettingsListTile(
              label: 'Notification',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () =>
                  nav.Router.push(context, const NotificationSettingsPage()),
            ),
            AppSettingsListTile(
              label: 'Change Password',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const ChangePasswordPage()),
            ),
            AppSettingsListTile(
              label: 'Change Phone Number',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () =>
                  nav.Router.push(context, const ChangePhoneNumberPage()),
            ),
            AppSettingsListTile(
              label: 'Edit Home Address',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const AddressPage()),
            ),
            AppSettingsListTile(
              label: 'Location',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () {},
            ),
            AppSettingsListTile(
              label: 'Profile Setting',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const ProfileEditPage()),
            ),
            AppSettingsListTile(
              label: 'Deactivate Account',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const IntroLoginPage()),
            ),
          ],
        ),
      ),
    );
  }
}
