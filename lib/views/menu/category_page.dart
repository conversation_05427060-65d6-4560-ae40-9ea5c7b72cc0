import 'package:flutter/material.dart';

import '../../core/components/app_back_button.dart';
import '../../core/components/product_tile_square.dart';
import '../../core/constants/constants.dart';
import '../../api/products.dart';
import '../../core/models/dummy_product_model.dart';

ProductModel _toProductModel(ProductDetailResponse p) {
  return ProductModel(
    name: p.title,
    weight: p.unit, // or any other field that makes sense
    cover: p.imageUrl ?? '',
    images: p.imageUrls,
    price: p.minPrice ?? 0,
    mainPrice: p.maxPrice ?? 0,
  );
}

class CategoryProductPage extends StatelessWidget {
  final int id;
  final String type; // 'category' or 'company'
  final String name;
  const CategoryProductPage(
      {super.key, required this.id, required this.type, required this.name});

  Future<List<ProductDetailResponse>> _fetchProducts() {
    if (type == 'category') {
      return ProductDetailsApiService.getProductsByCategoryId(id);
    } else {
      return ProductDetailsApiService.getProductsByCompanyId(id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(name),
        leading: const AppBackButton(),
      ),
      body: FutureBuilder<List<ProductDetailResponse>>(
        future: _fetchProducts(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text('خطأ في تحميل المنتجات'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('لا توجد منتجات'));
          }
          final products = snapshot.data!;
          return GridView.builder(
            padding: const EdgeInsets.only(top: AppDefaults.padding),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              childAspectRatio: 0.85,
            ),
            itemCount: products.length,
            itemBuilder: (context, index) {
              return ProductTileSquare(
                data: _toProductModel(products[index]),
                productId: products[index].id,
              );
            },
          );
        },
      ),
    );
  }
}
