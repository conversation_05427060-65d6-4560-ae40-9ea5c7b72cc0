import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../constants/app_icons.dart';
import '../constants/app_colors.dart';
import '../../utils/quantity_validation.dart';

/// A reusable quantity stepper component that respects min/max constraints
class QuantityStepper extends StatelessWidget {
  final int quantity;
  final int minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final ValueChanged<int> onQuantityChanged;
  final VoidCallback? onRemove;
  final bool showConstraintText;
  final String? productName;
  final bool enabled;

  const QuantityStepper({
    super.key,
    required this.quantity,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    required this.onQuantityChanged,
    this.onRemove,
    this.showConstraintText = true,
    this.productName,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final canDecrement = quantity > minimumOrderQuantity;
    final canIncrement = maximumOrderQuantity == null || quantity < maximumOrderQuantity!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildQuantityButton(
              context: context,
              icon: AppIcons.removeQuantity,
              onPressed: enabled
                  ? () {
                      if (canDecrement) {
                        onQuantityChanged(quantity - 1);
                      } else if (onRemove != null) {
                        onRemove!();
                      }
                    }
                  : null,
              isEnabled: enabled,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '$quantity',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: enabled ? Colors.black : Colors.grey,
                    ),
              ),
            ),
            _buildQuantityButton(
              context: context,
              icon: AppIcons.addQuantity,
              onPressed: enabled && canIncrement
                  ? () {
                      onQuantityChanged(quantity + 1);
                    }
                  : null,
              isEnabled: enabled && canIncrement,
            ),
          ],
        ),
        if (showConstraintText) ...[
          const SizedBox(height: 4),
          Text(
            _getQuantityConstraintText(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 11,
                ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuantityButton({
    required BuildContext context,
    required String icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isEnabled ? Colors.grey.shade300 : Colors.grey.shade200,
        ),
        color: isEnabled ? null : Colors.grey.shade50,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: SvgPicture.asset(
          icon,
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(
            isEnabled ? Colors.black : Colors.grey.shade400,
            BlendMode.srcIn,
          ),
        ),
        constraints: const BoxConstraints(),
        padding: EdgeInsets.zero,
      ),
    );
  }

  String _getQuantityConstraintText() {
    return QuantityValidation.getQuantityRangeText(
      minimumOrderQuantity: minimumOrderQuantity,
      maximumOrderQuantity: maximumOrderQuantity,
    );
  }
}

/// A compact version of the quantity stepper for use in product cards
class CompactQuantityStepper extends StatelessWidget {
  final int quantity;
  final int minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final ValueChanged<int> onQuantityChanged;
  final VoidCallback? onRemove;
  final bool enabled;

  const CompactQuantityStepper({
    super.key,
    required this.quantity,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    required this.onQuantityChanged,
    this.onRemove,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final canDecrement = quantity > minimumOrderQuantity;
    final canIncrement = maximumOrderQuantity == null || quantity < maximumOrderQuantity!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCompactButton(
          context: context,
          icon: Icons.remove,
          onPressed: enabled
              ? () {
                  if (canDecrement) {
                    onQuantityChanged(quantity - 1);
                  } else if (onRemove != null) {
                    onRemove!();
                  }
                }
              : null,
          isEnabled: enabled,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            '$quantity',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: enabled ? Colors.black : Colors.grey,
                ),
          ),
        ),
        _buildCompactButton(
          context: context,
          icon: Icons.add,
          onPressed: enabled && canIncrement
              ? () {
                  onQuantityChanged(quantity + 1);
                }
              : null,
          isEnabled: enabled && canIncrement,
        ),
      ],
    );
  }

  Widget _buildCompactButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
  }) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isEnabled ? AppColors.primary : Colors.grey.shade300,
          width: 1,
        ),
        color: isEnabled ? AppColors.primary.withValues(alpha: 0.1) : Colors.grey.shade50,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 14,
          color: isEnabled ? AppColors.primary : Colors.grey.shade400,
        ),
        constraints: const BoxConstraints(),
        padding: EdgeInsets.zero,
      ),
    );
  }
}

/// A quantity input field that validates against constraints
class QuantityInputField extends StatefulWidget {
  final int quantity;
  final int minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final ValueChanged<int> onQuantityChanged;
  final String? productName;
  final bool enabled;

  const QuantityInputField({
    super.key,
    required this.quantity,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    required this.onQuantityChanged,
    this.productName,
    this.enabled = true,
  });

  @override
  State<QuantityInputField> createState() => _QuantityInputFieldState();
}

class _QuantityInputFieldState extends State<QuantityInputField> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.quantity.toString());
  }

  @override
  void didUpdateWidget(QuantityInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quantity != widget.quantity) {
      _controller.text = widget.quantity.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validateAndUpdate(String value) {
    final quantity = int.tryParse(value);
    if (quantity == null) {
      setState(() {
        _errorText = 'يرجى إدخال رقم صحيح';
      });
      return;
    }

    final validation = QuantityValidation.validateQuantity(
      quantity: quantity,
      minimumOrderQuantity: widget.minimumOrderQuantity,
      maximumOrderQuantity: widget.maximumOrderQuantity,
      productName: widget.productName,
    );

    if (validation.isValid) {
      setState(() {
        _errorText = null;
      });
      widget.onQuantityChanged(quantity);
    } else {
      setState(() {
        _errorText = validation.errorMessage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          enabled: widget.enabled,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'الكمية',
            hintText: QuantityValidation.getQuantityRangeText(
              minimumOrderQuantity: widget.minimumOrderQuantity,
              maximumOrderQuantity: widget.maximumOrderQuantity,
            ),
            errorText: _errorText,
            border: const OutlineInputBorder(),
          ),
          onChanged: _validateAndUpdate,
          onFieldSubmitted: _validateAndUpdate,
        ),
      ],
    );
  }
}
