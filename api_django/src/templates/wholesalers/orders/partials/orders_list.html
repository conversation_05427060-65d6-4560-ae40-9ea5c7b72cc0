{% if orders %}
<div class="table-responsive">
    <table class="table table-hover mb-0">
        <thead>
            <tr>
                <th>رقم الطلب</th>
                <th>المتجر</th>
                <th>عدد المنتجات</th>
                <th>المبلغ الإجمالي</th>
                <th>الحالة</th>
                <th>تاريخ الطلب</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for order in orders %}
            {% include 'wholesalers/orders/partials/order_row.html' %}
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="d-flex justify-content-between align-items-center p-3 border-top">
    <div class="text-muted">
        عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} طلب
    </div>
    
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination pagination-sm mb-0">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-get="{% url 'orders_list' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-target="#ordersContainer">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                           hx-get="{% url 'orders_list' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                           hx-target="#ordersContainer">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}"
                       hx-get="{% url 'orders_list' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                       hx-target="#ordersContainer">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد طلبات</h4>
    <p class="text-muted">لم يتم العثور على طلبات تطابق المعايير المحددة</p>
    {% if request.GET %}
    <button class="btn btn-outline-primary" onclick="clearFilters()">
        <i class="fas fa-times me-2"></i>
        مسح الفلاتر
    </button>
    {% endif %}
</div>
{% endif %}
