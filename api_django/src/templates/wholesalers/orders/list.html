{% extends 'wholesalers/base.html' %}

{% block title %}إدارة الطلبات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-shopping-cart me-2 text-success"></i>
                إدارة الطلبات
            </h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success" onclick="refreshOrders()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث
                </button>
                <button class="btn btn-primary" onclick="exportOrders()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card orders">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="mb-1">{{ total_orders }}</h3>
                <p class="text-muted mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card alerts">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="mb-1">{{ pending_orders }}</h3>
                <p class="text-muted mb-0">في الانتظار</p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card sales">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="mb-1">{{ completed_orders }}</h3>
                <p class="text-muted mb-0">مكتملة</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" id="filterForm" hx-get="{% url 'orders_list' %}" hx-target="#ordersContainer" hx-trigger="change, submit">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">حالة الطلب</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>في الانتظار</option>
                        <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>قيد المعالجة</option>
                        <option value="shipped" {% if status_filter == 'shipped' %}selected{% endif %}>تم الشحن</option>
                        <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>تم التسليم</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="رقم الطلب أو اسم المتجر" value="{{ search_query }}">
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-2"></i>
                        مسح
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الطلبات
        </h5>
    </div>
    <div class="card-body p-0">
        <div id="ordersContainer">
            {% include 'wholesalers/orders/partials/orders_list.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshOrders() {
    htmx.trigger('#filterForm', 'submit');
}

function clearFilters() {
    document.getElementById('status').value = '';
    document.getElementById('search').value = '';
    document.getElementById('date_from').value = '';
    document.getElementById('date_to').value = '';
    htmx.trigger('#filterForm', 'submit');
}

function exportOrders() {
    // Get current filter parameters
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // Add export parameter
    params.append('export', 'true');
    
    // Create download link
    const url = '{% url "orders_list" %}?' + params.toString();
    window.open(url, '_blank');
}

function updateOrderStatus(orderId, newStatus) {
    if (confirm('هل أنت متأكد من تحديث حالة هذا الطلب؟')) {
        const formData = new FormData();
        formData.append('status', newStatus);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
        
        fetch(`/wholesaler/orders/${orderId}/update-status/`, {
            method: 'POST',
            body: formData,
            headers: {
                'HX-Request': 'true'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Update the specific order row
            const orderRow = document.querySelector(`[data-order-id="${orderId}"]`);
            if (orderRow) {
                orderRow.outerHTML = html;
            }
            
            // Refresh the page to update statistics
            setTimeout(() => {
                location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث حالة الطلب');
        });
    }
}

// Auto-refresh every 30 seconds for pending orders
setInterval(function() {
    if (document.getElementById('status').value === 'pending' || document.getElementById('status').value === '') {
        refreshOrders();
    }
}, 30000);
</script>
{% endblock %}
