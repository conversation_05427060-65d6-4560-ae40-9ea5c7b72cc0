import 'package:flutter/foundation.dart';
import '../api/wholesaler_api_service.dart';
import '../models/wholesaler_models.dart';

/// Repository class for managing wholesaler data and state
/// This provides a clean interface between the UI and the API layer
class WholesalerRepository extends ChangeNotifier {
  static final WholesalerRepository _instance = WholesalerRepository._internal();
  factory WholesalerRepository() => _instance;
  WholesalerRepository._internal();

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  // Loading states
  bool _isLoadingProfile = false;
  bool _isLoadingItems = false;
  bool _isLoadingMinCharges = false;
  final bool _isLoadingStats = false;

  // Data
  WholesalerProfile? _profile;
  List<WholesalerItem> _items = [];
  List<RegionMinCharge> _minCharges = [];
  WholesalerStats _stats = WholesalerStats.empty();

  // Error states
  String? _profileError;
  String? _itemsError;
  String? _minChargesError;
  String? _statsError;

  // ============================================================================
  // GETTERS
  // ============================================================================

  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingItems => _isLoadingItems;
  bool get isLoadingMinCharges => _isLoadingMinCharges;
  bool get isLoadingStats => _isLoadingStats;

  WholesalerProfile? get profile => _profile;
  List<WholesalerItem> get items => _items;
  List<RegionMinCharge> get minCharges => _minCharges;
  WholesalerStats get stats => _stats;

  String? get profileError => _profileError;
  String? get itemsError => _itemsError;
  String? get minChargesError => _minChargesError;
  String? get statsError => _statsError;

  bool get hasProfile => _profile != null;
  bool get hasItems => _items.isNotEmpty;
  bool get hasMinCharges => _minCharges.isNotEmpty;

  // ============================================================================
  // PROFILE OPERATIONS
  // ============================================================================

  /// Load wholesaler profile
  Future<void> loadProfile({bool forceRefresh = false}) async {
    if (_profile != null && !forceRefresh) return;

    _isLoadingProfile = true;
    _profileError = null;
    notifyListeners();

    try {
      _profile = await WholesalerApiService.getMyWholesaler();
      _profileError = null;
    } catch (e) {
      if (kDebugMode) rethrow;
      _profileError = e.toString();
    } finally {
      _isLoadingProfile = false;
      notifyListeners();
    }
  }

  /// Update wholesaler profile
  Future<bool> updateProfile(WholesalerUpdateRequest request) async {
    try {
      _profile = await WholesalerApiService.updateMyWholesaler(request);
      _profileError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _profileError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Upload logo
  Future<bool> uploadLogo(String filePath) async {
    try {
      _profile = await WholesalerApiService.uploadLogo(filePath);
      _profileError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _profileError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Upload background image
  Future<bool> uploadBackground(String filePath) async {
    try {
      _profile = await WholesalerApiService.uploadBackground(filePath);
      _profileError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _profileError = e.toString();
      notifyListeners();
      return false;
    }
  }

  // ============================================================================
  // ITEM OPERATIONS
  // ============================================================================

  /// Load all items
  Future<void> loadItems({bool forceRefresh = false}) async {
    if (_items.isNotEmpty && !forceRefresh) return;

    _isLoadingItems = true;
    _itemsError = null;
    notifyListeners();

    try {
      _items = await WholesalerApiService.listItems();
      _itemsError = null;
      _updateStats();
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
    } finally {
      _isLoadingItems = false;
      notifyListeners();
    }
  }

  /// Create new item
  Future<bool> createItem(WholesalerItemCreateRequest request) async {
    try {
      final newItem = await WholesalerApiService.createItem(request);
      _items.add(newItem);
      _itemsError = null;
      _updateStats();
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Update existing item
  Future<bool> updateItem(int itemId, WholesalerItemUpdateRequest request) async {
    try {
      final updatedItem = await WholesalerApiService.updateItem(itemId, request);
      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        _items[index] = updatedItem;
        _updateStats();
        notifyListeners();
      }
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Delete item
  Future<bool> deleteItem(int itemId) async {
    try {
      await WholesalerApiService.deleteItem(itemId);
      _items.removeWhere((item) => item.id == itemId);
      _updateStats();
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Expire item
  Future<bool> expireItem(int itemId) async {
    try {
      await WholesalerApiService.expireItem(itemId);
      // Refresh the item to get updated expiry status
      await loadItems(forceRefresh: true);
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Update inventory for an item
  Future<bool> updateInventory(int itemId, InventoryTransactionRequest request) async {
    try {
      final response = await WholesalerApiService.updateInventory(itemId, request);
      
      // Update the item in our local list
      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        _items[index] = _items[index].copyWith(
          inventoryCount: response.newInventoryCount,
        );
        _updateStats();
        notifyListeners();
      }
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _itemsError = e.toString();
      notifyListeners();
      return false;
    }
  }

  // ============================================================================
  // MIN CHARGE OPERATIONS
  // ============================================================================

  /// Load minimum charges
  Future<void> loadMinCharges({bool forceRefresh = false}) async {
    if (_minCharges.isNotEmpty && !forceRefresh) return;

    _isLoadingMinCharges = true;
    _minChargesError = null;
    notifyListeners();

    try {
      _minCharges = await WholesalerApiService.listMinCharges();
      _minChargesError = null;
    } catch (e) {
      if (kDebugMode) rethrow;
      _minChargesError = e.toString();
    } finally {
      _isLoadingMinCharges = false;
      notifyListeners();
    }
  }

  /// Create or update minimum charge
  Future<bool> createMinCharge(RegionMinChargeRequest request) async {
    try {
      final newMinCharge = await WholesalerApiService.createMinCharge(request);
      
      // Remove existing min charge for the same region if it exists
      _minCharges.removeWhere((charge) => charge.region.id == request.regionId);
      
      // Add the new/updated min charge
      _minCharges.add(newMinCharge);
      _minChargesError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _minChargesError = e.toString();
      notifyListeners();
      return false;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      loadProfile(forceRefresh: true),
      loadItems(forceRefresh: true),
      loadMinCharges(forceRefresh: true),
    ]);
  }

  /// Update statistics based on current items
  void _updateStats() {
    final now = DateTime.now();
    
    final totalProducts = _items.length;
    final lowStockItems = _items.where((item) => item.inventoryCount < 10).length;
    final expiredItems = _items.where((item) => 
      item.priceExpiry != null && item.priceExpiry!.isBefore(now)
    ).length;

    _stats = _stats.copyWith(
      totalProducts: totalProducts,
      lowStockItems: lowStockItems,
      expiredItems: expiredItems,
    );
  }

  /// Get item by ID
  WholesalerItem? getItemById(int itemId) {
    try {
      return _items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  /// Get items with low stock
  List<WholesalerItem> getLowStockItems({int threshold = 10}) {
    return _items.where((item) => item.inventoryCount < threshold).toList();
  }

  /// Get expired items
  List<WholesalerItem> getExpiredItems() {
    final now = DateTime.now();
    return _items.where((item) => 
      item.priceExpiry != null && item.priceExpiry!.isBefore(now)
    ).toList();
  }

  /// Clear all data (useful for logout)
  void clearData() {
    _profile = null;
    _items.clear();
    _minCharges.clear();
    _stats = WholesalerStats.empty();
    
    _profileError = null;
    _itemsError = null;
    _minChargesError = null;
    _statsError = null;
    
    notifyListeners();
  }
}
