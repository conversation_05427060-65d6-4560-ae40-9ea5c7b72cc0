from django.conf import settings
from django.http import HttpResponseNotFound
from django.shortcuts import redirect


class AdminHostRestrictionMiddleware:
    """
    Middleware that restricts access to the admin site based on the hostname.
    In production mode (DEBUG=False), only allows access from internal.tagerplus.com
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if the request is for the admin site
        if request.path.startswith("/admin/"):
            # In production mode, check hostname
            if not settings.DEBUG and request.get_host() != "internal.tagerplus.com":
                return HttpResponseNotFound("Not Found.")
        # Check if the request is for the API
        elif request.path.startswith("/api/"):
            # In production mode, check hostname
            if not settings.DEBUG and request.get_host() != "api.tagerplus.com":
                return HttpResponseNotFound("Not Found.")
        # Check if the request is for the wholesaler site
        elif request.path.startswith("/wholesaler/"):
            # In production mode, check hostname
            if not settings.DEBUG and request.get_host() != "gomla.tagerplus.com":
                return HttpResponseNotFound("Not Found.")

        return self.get_response(request)
