{% if products %}
<div class="row g-3 p-3">
    {% for product in products %}
    <div class="col-lg-3 col-md-4 col-sm-6">
        <div class="card product-card h-100 position-relative" onclick="toggleProductSelection({{ product.id }})">
            <!-- Selection Checkbox -->
            <div class="product-checkbox">
                <input type="checkbox" 
                       class="form-check-input" 
                       data-product-id="{{ product.id }}"
                       onchange="toggleProduct({{ product.id }})"
                       onclick="event.stopPropagation()">
            </div>
            
            <!-- Selection Badge -->
            <div class="selection-badge" style="display: none;">
                <span class="badge bg-success">
                    <i class="fas fa-check"></i>
                </span>
            </div>
            
            <div class="card-body p-3">
                <!-- Product Image -->
                <div class="text-center mb-3">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" 
                         alt="{{ product.name }}" 
                         class="product-image">
                    {% else %}
                    <div class="product-placeholder">
                        <i class="fas fa-image fa-2x"></i>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Product Info -->
                <div class="text-center">
                    <h6 class="card-title mb-2">{{ product.name }}</h6>
                    <p class="card-text text-muted small mb-2">{{ product.title|truncatechars:40 }}</p>
                    
                    <!-- Company and Category -->
                    <div class="mb-2">
                        {% if product.company %}
                        <span class="badge bg-light text-dark me-1">{{ product.company.name }}</span>
                        {% endif %}
                        {% if product.category %}
                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                        {% endif %}
                    </div>
                    
                    <!-- Barcode -->
                    {% if product.barcode %}
                    <small class="text-muted d-block mb-2">{{ product.barcode }}</small>
                    {% endif %}
                    
                    <!-- Default Values Info -->
                    <div class="mt-2 p-2 bg-light rounded">
                        <small class="text-muted d-block">القيم الافتراضية:</small>
                        <small class="text-success d-block">السعر: 0.00 ج.م | المخزون: 0</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="d-flex justify-content-between align-items-center p-3 border-top">
    <div class="text-muted">
        عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} منتج
    </div>
    
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination pagination-sm mb-0">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-get="{% url 'bulk_add_items' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-target="#productsContainer">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                           hx-get="{% url 'bulk_add_items' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                           hx-target="#productsContainer">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}"
                       hx-get="{% url 'bulk_add_items' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                       hx-target="#productsContainer">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

{% else %}
<div class="text-center py-5">
    {% if search_query or company_filter or category_filter %}
    <i class="fas fa-search fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد نتائج</h4>
    <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث المحددة</p>
    <button class="btn btn-outline-primary" onclick="clearFilters()">
        <i class="fas fa-times me-2"></i>
        مسح الفلاتر
    </button>
    {% else %}
    <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد منتجات متاحة</h4>
    <p class="text-muted">جميع المنتجات موجودة بالفعل في مخزونك أو لا توجد منتجات في النظام</p>
    <div class="d-flex gap-2 justify-content-center">
        <a href="{% url 'items_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمخزون
        </a>
        <a href="{% url 'item_create_step1' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج واحد
        </a>
    </div>
    {% endif %}
</div>
{% endif %}

<script>
function toggleProductSelection(productId) {
    const checkbox = document.querySelector(`input[data-product-id="${productId}"]`);
    checkbox.checked = !checkbox.checked;
    toggleProduct(productId);
}

// Update selection badges
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[data-product-id]');
    checkboxes.forEach(checkbox => {
        const productId = checkbox.getAttribute('data-product-id');
        const card = checkbox.closest('.product-card');
        const badge = card.querySelector('.selection-badge');
        
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                badge.style.display = 'block';
                card.classList.add('selected');
            } else {
                badge.style.display = 'none';
                card.classList.remove('selected');
            }
        });
    });
});
</script>
