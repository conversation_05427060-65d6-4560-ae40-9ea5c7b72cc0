import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../core/constants/app_icons.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/constants/app_colors.dart';
import '../../core/components/network_image.dart';
import '../../services/favorites_service.dart';
import '../../api/products.dart';
import '../entrypoint/components/app_navigation_bar.dart';

class SavePage extends StatefulWidget {
  const SavePage({
    super.key,
    this.isHomePage = false,
  });

  final bool isHomePage;

  @override
  State<SavePage> createState() => _SavePageState();
}

class _SavePageState extends State<SavePage> {
  List<int> _favoriteIds = [];
  bool _loading = true;
  Map<int, ProductDetailResponse> _products = {};

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _loading = true;
    });
    final ids = await FavoritesService.instance.getFavorites();
    final Map<int, ProductDetailResponse> products = {};
    for (final id in ids) {
      try {
        final product = await ProductDetailsApiService.getProductById(id);
        products[id] = product;
      } catch (_) {}
    }
    if (mounted) {
      setState(() {
        _favoriteIds = ids;
        _products = products;
        _loading = false;
      });
    }
  }

  Future<void> _removeFavorite(int productId) async {
    await FavoritesService.instance.removeFavorite(productId);
    await _loadFavorites();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تمت إزالة المنتج من المفضلة')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final body = _loading
        ? const Center(child: CircularProgressIndicator())
        : _favoriteIds.isEmpty
            ? _buildEmptyState(context)
            : RefreshIndicator(
                onRefresh: _loadFavorites,
                child: ListView.separated(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                  itemCount: _favoriteIds.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, index) {
                    final id = _favoriteIds[index];
                    final product = _products[id];
                    if (product == null) return const SizedBox.shrink();
                    return _buildProductCard(context, product);
                  },
                ),
              );

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: _buildAppBar(context),
      body: SafeArea(child: body),
      bottomNavigationBar: widget.isHomePage
          ? AppBottomNavigationBar(currentIndex: 3, onNavTap: _onNavTap)
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.scaffoldBackground,
      elevation: 0,
      centerTitle: true,
      automaticallyImplyLeading: false,
      title: Text(
        'المحفوظات',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
      actions: const [],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(AppIcons.save,
                height: 80, color: AppColors.primary),
            const SizedBox(height: 24),
            Text(
              'لا توجد منتجات محفوظة في المفضلة',
              style: Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك حفظ المنتجات المفضلة لديك من خلال الضغط على أيقونة القلب في صفحة المنتج.',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductCard(
      BuildContext context, ProductDetailResponse product) {
    final minPrice = product.minPrice;
    return Material(
      borderRadius: AppDefaults.borderRadius,
      color: Colors.white,
      elevation: 1,
      child: InkWell(
        borderRadius: AppDefaults.borderRadius,
        onTap: () {
          Navigator.pushNamed(
            context,
            '/productDetails',
            arguments: {'productId': product.id},
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: NetworkImageWithLoader(
                  product.imageUrl ?? '',
                  fit: BoxFit.cover,
                  radius: 16,
                  // Make the image bigger
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const SizedBox(width: 20),
              // Make the image take more space
              SizedBox(
                width: 80,
                height: 80,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: NetworkImageWithLoader(
                    product.imageUrl ?? '',
                    fit: BoxFit.cover,
                    radius: 16,
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.title,
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (product.company != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Text(
                          product.company!.title,
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ),
                    if (minPrice != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          'من ${minPrice.toStringAsFixed(2)} ر.س',
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge
                              ?.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold),
                        ),
                      ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => _removeFavorite(product.id),
                tooltip: 'إزالة من المفضلة',
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onNavTap(int index) {
    // Navigate to the selected tab using the main entry point
    // This assumes you use a main EntryPointUI with tabs
    Navigator.of(context).pushReplacementNamed(_getRouteForIndex(index));
  }

  String _getRouteForIndex(int index) {
    switch (index) {
      case 0:
        return '/';
      case 1:
        return '/menu';
      case 2:
        return '/cart';
      case 3:
        return '/save';
      case 4:
        return '/profile';
      default:
        return '/';
    }
  }
}
