import 'package:flutter/material.dart';

import '../../../utils/navigation.dart' as nav;
import '../../../views/auth/sign_up_page.dart';

class DontHaveAccountRow extends StatelessWidget {
  const DontHaveAccountRow({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('ليس لديك حساب؟'),
        TextButton(
          onPressed: () => nav.Router.push(context, const SignUpPage()),
          child: const Text('إنشاء حساب'),
        ),
      ],
    );
  }
}
