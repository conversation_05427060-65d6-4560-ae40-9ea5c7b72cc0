{% extends 'wholesalers/base.html' %}

{% block title %}تعديل المنتج - {{ item.product.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-edit me-2 text-success"></i>
                    تعديل المنتج
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'wholesaler_dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'items_list' %}">المنتجات</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'item_detail' item.id %}">{{ item.product.name }}</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'item_detail' item.id %}" class="btn btn-outline-info">
                    <i class="fas fa-eye me-2"></i>
                    عرض التفاصيل
                </a>
                <a href="{% url 'items_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Edit Form -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل معلومات المنتج
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="editForm">
                    {% csrf_token %}
                    
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label for="base_price" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                السعر الأساسي *
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control" 
                                       id="base_price" 
                                       name="base_price" 
                                       value="{{ item.base_price }}"
                                       step="0.01" 
                                       min="0"
                                       required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <div class="form-text">السعر الذي ستبيع به هذا المنتج</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="inventory_count" class="form-label">
                                <i class="fas fa-warehouse me-1"></i>
                                الكمية المتوفرة
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="inventory_count" 
                                   name="inventory_count" 
                                   value="{{ item.inventory_count }}"
                                   min="0">
                            <div class="form-text">الكمية الحالية في المخزون</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="minimum_order_quantity" class="form-label">
                                <i class="fas fa-arrow-down me-1"></i>
                                الحد الأدنى للطلب *
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="minimum_order_quantity" 
                                   name="minimum_order_quantity" 
                                   value="{{ item.minimum_order_quantity }}"
                                   min="1"
                                   required>
                            <div class="form-text">أقل كمية يمكن طلبها</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="maximum_order_quantity" class="form-label">
                                <i class="fas fa-arrow-up me-1"></i>
                                الحد الأقصى للطلب
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="maximum_order_quantity" 
                                   name="maximum_order_quantity" 
                                   value="{{ item.maximum_order_quantity|default:'' }}"
                                   min="1"
                                   placeholder="غير محدد">
                            <div class="form-text">أكبر كمية يمكن طلبها (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تأكد من صحة البيانات قبل الحفظ. سيتم تطبيق التغييرات فوراً.
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <div class="d-flex gap-2">
                            <a href="{% url 'item_detail' item.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="button" class="btn btn-outline-warning" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                        </div>
                        <button type="submit" class="btn btn-success" id="saveBtn">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <button class="btn btn-outline-success w-100" onclick="quickSetPrice()">
                            <i class="fas fa-tag me-2"></i>
                            تسعير سريع
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-info w-100" onclick="quickAddStock()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مخزون
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-warning w-100" onclick="quickSetLimits()">
                            <i class="fas fa-sliders-h me-2"></i>
                            تحديد الحدود
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Preview -->
    <div class="col-lg-4">
        <!-- Product Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المنتج
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if item.product.image %}
                    <img src="{{ item.product.image.url }}" 
                         alt="{{ item.product.name }}" 
                         class="img-fluid rounded shadow-sm"
                         style="max-height: 200px;">
                    {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center shadow-sm" 
                         style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    {% endif %}
                </div>
                
                <h6 class="mb-2">{{ item.product.name }}</h6>
                <p class="text-muted small mb-3">{{ item.product.title }}</p>
                
                <table class="table table-borderless table-sm">
                    {% if item.product.company %}
                    <tr>
                        <td class="text-muted">الشركة:</td>
                        <td class="fw-semibold">{{ item.product.company.name }}</td>
                    </tr>
                    {% endif %}
                    {% if item.product.category %}
                    <tr>
                        <td class="text-muted">الفئة:</td>
                        <td class="fw-semibold">{{ item.product.category.name }}</td>
                    </tr>
                    {% endif %}
                    {% if item.product.barcode %}
                    <tr>
                        <td class="text-muted">الباركود:</td>
                        <td class="fw-semibold font-monospace">{{ item.product.barcode }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="text-muted">تاريخ الإضافة:</td>
                        <td class="fw-semibold">{{ item.created_at|date:"Y/m/d" }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Current Values -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    القيم الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3 text-center">
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h6 class="text-success mb-1" id="currentPrice">{{ item.base_price }} ج.م</h6>
                            <small class="text-muted">السعر</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h6 class="text-info mb-1" id="currentStock">{{ item.inventory_count }}</h6>
                            <small class="text-muted">المخزون</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h6 class="text-warning mb-1" id="currentMin">{{ item.minimum_order_quantity }}</h6>
                            <small class="text-muted">الحد الأدنى</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h6 class="text-primary mb-1" id="currentMax">{{ item.maximum_order_quantity|default:"∞" }}</h6>
                            <small class="text-muted">الحد الأقصى</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">الحالة:</span>
                        <span class="badge bg-success" id="statusBadge">
                            {% if item.base_price > 0 and item.inventory_count > 0 %}
                            متاح للطلب
                            {% else %}
                            يحتاج تحديث
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Store original values for reset functionality
const originalValues = {
    base_price: {{ item.base_price }},
    inventory_count: {{ item.inventory_count }},
    minimum_order_quantity: {{ item.minimum_order_quantity }},
    maximum_order_quantity: {{ item.maximum_order_quantity|default:"null" }}
};

function resetForm() {
    if (confirm('هل تريد إعادة تعيين جميع القيم إلى حالتها الأصلية؟')) {
        document.getElementById('base_price').value = originalValues.base_price;
        document.getElementById('inventory_count').value = originalValues.inventory_count;
        document.getElementById('minimum_order_quantity').value = originalValues.minimum_order_quantity;
        document.getElementById('maximum_order_quantity').value = originalValues.maximum_order_quantity || '';
        updatePreview();
    }
}

function updatePreview() {
    const price = document.getElementById('base_price').value || 0;
    const stock = document.getElementById('inventory_count').value || 0;
    const minOrder = document.getElementById('minimum_order_quantity').value || 1;
    const maxOrder = document.getElementById('maximum_order_quantity').value || '∞';
    
    document.getElementById('currentPrice').textContent = price + ' ج.م';
    document.getElementById('currentStock').textContent = stock;
    document.getElementById('currentMin').textContent = minOrder;
    document.getElementById('currentMax').textContent = maxOrder;
    
    // Update status
    const statusBadge = document.getElementById('statusBadge');
    if (parseFloat(price) > 0 && parseInt(stock) > 0) {
        statusBadge.textContent = 'متاح للطلب';
        statusBadge.className = 'badge bg-success';
    } else {
        statusBadge.textContent = 'يحتاج تحديث';
        statusBadge.className = 'badge bg-warning';
    }
}

function quickSetPrice() {
    const currentPrice = document.getElementById('base_price').value;
    const newPrice = prompt('أدخل السعر الجديد:', currentPrice);
    if (newPrice !== null && !isNaN(newPrice) && parseFloat(newPrice) >= 0) {
        document.getElementById('base_price').value = newPrice;
        updatePreview();
    }
}

function quickAddStock() {
    const currentStock = parseInt(document.getElementById('inventory_count').value) || 0;
    const addAmount = prompt('كم تريد إضافة للمخزون؟', '10');
    if (addAmount !== null && !isNaN(addAmount) && parseInt(addAmount) > 0) {
        const newStock = currentStock + parseInt(addAmount);
        document.getElementById('inventory_count').value = newStock;
        updatePreview();
    }
}

function quickSetLimits() {
    const minOrder = prompt('الحد الأدنى للطلب:', document.getElementById('minimum_order_quantity').value);
    if (minOrder !== null && !isNaN(minOrder) && parseInt(minOrder) > 0) {
        document.getElementById('minimum_order_quantity').value = minOrder;
        
        const maxOrder = prompt('الحد الأقصى للطلب (اتركه فارغاً لعدم التحديد):', document.getElementById('maximum_order_quantity').value);
        if (maxOrder !== null) {
            document.getElementById('maximum_order_quantity').value = maxOrder;
        }
        updatePreview();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for real-time preview updates
    const inputs = ['base_price', 'inventory_count', 'minimum_order_quantity', 'maximum_order_quantity'];
    inputs.forEach(inputId => {
        document.getElementById(inputId).addEventListener('input', updatePreview);
    });
    
    // Form validation
    const form = document.getElementById('editForm');
    const saveBtn = document.getElementById('saveBtn');
    
    form.addEventListener('submit', function(e) {
        const basePrice = document.getElementById('base_price').value;
        const minOrder = document.getElementById('minimum_order_quantity').value;
        const maxOrder = document.getElementById('maximum_order_quantity').value;
        
        // Validate price
        if (!basePrice || parseFloat(basePrice) < 0) {
            e.preventDefault();
            alert('يرجى إدخال سعر صحيح');
            document.getElementById('base_price').focus();
            return;
        }
        
        // Validate order quantities
        if (maxOrder && parseInt(maxOrder) < parseInt(minOrder)) {
            e.preventDefault();
            alert('الحد الأقصى للطلب يجب أن يكون أكبر من أو يساوي الحد الأدنى');
            document.getElementById('maximum_order_quantity').focus();
            return;
        }
        
        // Show loading state
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    });
    
    // Auto-focus on price input
    document.getElementById('base_price').focus();
});
</script>
{% endblock %}
