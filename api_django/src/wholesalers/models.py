from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator

from accounts.models import CustomUser
from core.utils import RandomFileName
from products.models import Region


class WholeSalerCategory(models.TextChoices):
    PHARMACEUTICAL = "PHARMACEUTICAL", "Pharmaceutical"
    GROCERY = "GROCERY", "Grocery"
    ELECTRONICS = "ELECTRONICS", "Electronics"


class PricingType(models.TextChoices):
    PER_PIECE = "PER_PIECE", "Per Piece"
    OVERALL = "OVERALL", "Overall"


class MeasurementUnit(models.TextChoices):
    PIECE = "PIECE", "Piece"
    KILOGRAM = "KILOGRAM", "Kilogram (kg)"
    GRAM = "GRAM", "Gram (g)"
    LITER = "LITER", "Liter (L)"
    MILLILITER = "MILLILITER", "Milliliter (mL)"
    METER = "METER", "Meter (m)"
    CENTIMETER = "CENTIMETER", "Centimeter (cm)"
    BOX = "BOX", "Box"
    CARTON = "CARTON", "Carton"
    PACK = "PACK", "Pack"
    BOTTLE = "BOTTLE", "Bottle"
    CAN = "CAN", "Can"
    DOZEN = "DOZEN", "Dozen"
    PAIR = "PAIR", "Pair"
    OTHER = "OTHER", "Other"


class Wholesaler(models.Model):
    category = models.CharField(
        max_length=255,
        choices=WholeSalerCategory.choices,
        default=WholeSalerCategory.GROCERY,
    )
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="wholesalers"
    )
    title = models.CharField(max_length=255, db_index=True)
    username = models.CharField(max_length=255, unique=True)
    background_image = models.ImageField(
        upload_to=RandomFileName("wholesalers/backgrounds/"),
        blank=True,
    )
    logo = models.ImageField(
        upload_to=RandomFileName("wholesalers/logos/"),
        blank=True,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    class Meta:
        verbose_name = "Wholesaler"
        verbose_name_plural = "Wholesalers"

    def __str__(self):
        return f"{self.title} ({self.username})"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Wholesaler, self).delete(*args, **kwargs)


def default_price_expiry():
    return timezone.now() + timezone.timedelta(days=3)


# Minimum charge and items for each Region for wholesaler
class RegionMinCharge(models.Model):
    wholesaler = models.ForeignKey(
        Wholesaler, on_delete=models.CASCADE, related_name="region_min_charge"
    )
    region = models.ForeignKey(
        Region, on_delete=models.CASCADE, related_name="wholesaler_min_charge"
    )
    min_charge = models.DecimalField(max_digits=10, decimal_places=2)
    min_items = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Region Min Charge"
        verbose_name_plural = "Region Min Charges"
        unique_together = ["wholesaler", "region"]
        indexes = [
            models.Index(fields=["wholesaler", "region"]),
            models.Index(fields=["region", "min_charge"]),
        ]


class Item(models.Model):
    wholesaler = models.ForeignKey(
        Wholesaler, on_delete=models.CASCADE, related_name="items"
    )
    product = models.ForeignKey(
        "products.Product", on_delete=models.CASCADE, related_name="wholesalers"
    )
    # Price for the item
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Base/Total price for this item (unit * quantity)",
    )
    # Inventory tracking
    inventory_count = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Current inventory count for this item",
    )
    # Order quantity constraints
    minimum_order_quantity = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Minimum quantity that can be ordered for this item",
    )
    maximum_order_quantity = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1)],
        help_text="Maximum quantity that can be ordered for this item (optional)",
    )
    price_expiry = models.DateTimeField(default=default_price_expiry)
    expires_at = models.DateTimeField(default=default_price_expiry)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    class Meta:
        verbose_name = "Item"
        verbose_name_plural = "Items"
        indexes = [
            models.Index(fields=["wholesaler", "product"]),
        ]

    def __str__(self):
        return f"{self.product.title} from {self.wholesaler.title}"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Item, self).delete(*args, **kwargs)


class InventoryTransactionType(models.TextChoices):
    ADDITION = "ADDITION", "Addition"
    SUBTRACTION = "SUBTRACTION", "Subtraction"


class InventoryTransaction(models.Model):
    """Model to track inventory changes for items"""

    item = models.ForeignKey(
        Item, on_delete=models.CASCADE, related_name="inventory_transactions"
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=InventoryTransactionType.choices,
        help_text="Type of inventory transaction (addition or subtraction)",
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        help_text="Quantity added or removed from inventory",
    )
    notes = models.TextField(
        blank=True,
        null=True,
        help_text="Optional notes about this inventory transaction",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Inventory Transaction"
        verbose_name_plural = "Inventory Transactions"
        indexes = [
            models.Index(fields=["item", "transaction_type"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        action = (
            "Added"
            if self.transaction_type == InventoryTransactionType.ADDITION
            else "Removed"
        )
        return f"{action} {self.quantity} of {self.item.product.title}"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(InventoryTransaction, self).delete(*args, **kwargs)
