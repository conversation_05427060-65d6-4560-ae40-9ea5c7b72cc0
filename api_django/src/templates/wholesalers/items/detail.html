{% extends 'wholesalers/base.html' %}

{% block title %}تفاصيل المنتج - {{ item.product.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-box me-2 text-success"></i>
                    تفاصيل المنتج
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'wholesaler_dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'items_list' %}">المنتجات</a></li>
                        <li class="breadcrumb-item active">{{ item.product.name }}</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'item_edit' item.id %}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المنتج
                </a>
                <a href="{% url 'items_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Product Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المنتج
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        {% if item.product.image %}
                        <img src="{{ item.product.image.url }}" 
                             alt="{{ item.product.name }}" 
                             class="img-fluid rounded shadow-sm">
                        {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center shadow-sm" 
                             style="height: 250px;">
                            <i class="fas fa-image fa-4x text-muted"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <h3 class="mb-3">{{ item.product.name }}</h3>
                        <p class="text-muted mb-3">{{ item.product.title }}</p>
                        
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">الشركة</h6>
                                    {% if item.product.company %}
                                    <p class="mb-0 fw-semibold">{{ item.product.company.name }}</p>
                                    {% else %}
                                    <p class="mb-0 text-muted">غير محدد</p>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">الفئة</h6>
                                    {% if item.product.category %}
                                    <p class="mb-0 fw-semibold">{{ item.product.category.name }}</p>
                                    {% else %}
                                    <p class="mb-0 text-muted">غير محدد</p>
                                    {% endif %}
                                </div>
                            </div>
                            {% if item.product.barcode %}
                            <div class="col-sm-6">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">الباركود</h6>
                                    <p class="mb-0 fw-semibold font-monospace">{{ item.product.barcode }}</p>
                                </div>
                            </div>
                            {% endif %}
                            <div class="col-sm-6">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">تاريخ الإضافة</h6>
                                    <p class="mb-0 fw-semibold">{{ item.created_at|date:"Y/m/d H:i" }}</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if item.product.description %}
                        <div class="mt-3">
                            <h6 class="text-muted mb-2">الوصف</h6>
                            <p class="text-muted">{{ item.product.description }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    معلومات المخزون والتسعير
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="text-center p-4 border rounded">
                            <div class="mb-3">
                                <i class="fas fa-tag fa-2x text-success"></i>
                            </div>
                            <h4 class="text-success mb-1">{{ item.base_price }} ج.م</h4>
                            <p class="text-muted mb-0">السعر الأساسي</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center p-4 border rounded">
                            <div class="mb-3">
                                {% if item.inventory_count == 0 %}
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                                {% elif item.inventory_count < 10 %}
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                {% else %}
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                                {% endif %}
                            </div>
                            <h4 class="mb-1">{{ item.inventory_count }}</h4>
                            <p class="text-muted mb-0">الكمية المتوفرة</p>
                            {% if item.inventory_count == 0 %}
                                <span class="badge bg-danger mt-1">نفد المخزون</span>
                            {% elif item.inventory_count < 10 %}
                                <span class="badge bg-warning mt-1">مخزون منخفض</span>
                            {% else %}
                                <span class="badge bg-success mt-1">متوفر</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center p-4 border rounded">
                            <div class="mb-3">
                                <i class="fas fa-arrow-down fa-2x text-info"></i>
                            </div>
                            <h4 class="text-info mb-1">{{ item.minimum_order_quantity }}</h4>
                            <p class="text-muted mb-0">الحد الأدنى للطلب</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center p-4 border rounded">
                            <div class="mb-3">
                                <i class="fas fa-arrow-up fa-2x text-primary"></i>
                            </div>
                            <h4 class="text-primary mb-1">{{ item.maximum_order_quantity|default:"غير محدد" }}</h4>
                            <p class="text-muted mb-0">الحد الأقصى للطلب</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions & Statistics -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'item_edit' item.id %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج
                    </a>
                    <button class="btn btn-outline-success" onclick="updateInventory()">
                        <i class="fas fa-plus me-2"></i>
                        تحديث المخزون
                    </button>
                    <button class="btn btn-outline-info" onclick="updatePrice()">
                        <i class="fas fa-tag me-2"></i>
                        تحديث السعر
                    </button>
                    <hr>
                    <button class="btn btn-outline-warning" onclick="duplicateItem()">
                        <i class="fas fa-copy me-2"></i>
                        نسخ المنتج
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteItem()">
                        <i class="fas fa-trash me-2"></i>
                        حذف المنتج
                    </button>
                </div>
            </div>
        </div>

        <!-- Item Status -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    حالة المنتج
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">حالة السعر</span>
                        {% if item.base_price > 0 %}
                        <span class="badge bg-success">محدد</span>
                        {% else %}
                        <span class="badge bg-warning">يحتاج تحديد</span>
                        {% endif %}
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">حالة المخزون</span>
                        {% if item.inventory_count == 0 %}
                        <span class="badge bg-danger">نفد</span>
                        {% elif item.inventory_count < 10 %}
                        <span class="badge bg-warning">منخفض</span>
                        {% else %}
                        <span class="badge bg-success">متوفر</span>
                        {% endif %}
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">حالة الطلب</span>
                        {% if item.base_price > 0 and item.inventory_count > 0 %}
                        <span class="badge bg-success">متاح للطلب</span>
                        {% else %}
                        <span class="badge bg-secondary">غير متاح</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted d-block">آخر تحديث</small>
                    <small class="fw-semibold">{{ item.updated_at|timesince }} مضت</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Update Modals -->
<div class="modal fade" id="updateInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="inventoryForm">
                    <div class="mb-3">
                        <label for="newInventory" class="form-label">الكمية الجديدة</label>
                        <input type="number" class="form-control" id="newInventory" value="{{ item.inventory_count }}" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveInventory()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="updatePriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث السعر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="priceForm">
                    <div class="mb-3">
                        <label for="newPrice" class="form-label">السعر الجديد</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="newPrice" value="{{ item.base_price }}" step="0.01" min="0">
                            <span class="input-group-text">ج.م</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="savePrice()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateInventory() {
    const modal = new bootstrap.Modal(document.getElementById('updateInventoryModal'));
    modal.show();
}

function updatePrice() {
    const modal = new bootstrap.Modal(document.getElementById('updatePriceModal'));
    modal.show();
}

function saveInventory() {
    const newInventory = document.getElementById('newInventory').value;
    quickUpdate('inventory_count', newInventory);
}

function savePrice() {
    const newPrice = document.getElementById('newPrice').value;
    quickUpdate('base_price', newPrice);
}

function quickUpdate(field, value) {
    const formData = new FormData();
    formData.append(field, value);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch('{% url "item_edit" item.id %}', {
        method: 'POST',
        body: formData,
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء التحديث');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء التحديث');
    });
}

function duplicateItem() {
    if (confirm('هل تريد نسخ هذا المنتج؟')) {
        // Implementation for duplicating item
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}

function deleteItem() {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.')) {
        // Implementation for deleting item
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}
</script>
{% endblock %}
