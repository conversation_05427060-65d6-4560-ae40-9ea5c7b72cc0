import 'package:flutter/material.dart';

import '../../../core/components/network_image.dart';
import '../../../core/constants/constants.dart';
import '../../../models/cart_models.dart';

class CartWholesalerHeader extends StatelessWidget {
  final Cart cart;

  const CartWholesalerHeader({
    super.key,
    required this.cart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Title
          Text(
            'تفاصيل السلة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontFamily: '<PERSON><PERSON>',
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 16),

          // Wholesaler Info
          Row(
            children: [
              // Wholesaler Logo
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: cart.wholesalerLogo != null
                      ? NetworkImageWithLoader(
                          cart.wholesalerLogo!,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          color: Colors.grey.shade100,
                          child: Icon(
                            Icons.store,
                            color: Colors.grey.shade400,
                            size: 24,
                          ),
                        ),
                ),
              ),
              const SizedBox(width: 12),

              // Wholesaler Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'التاجر: ${cart.wholesalerTitle}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontFamily: 'Gilroy',
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'جميع المنتجات من نفس التاجر',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'Gilroy',
                            color: Colors.grey.shade600,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Cart Summary
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Items Count
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'العناصر',
                    '${cart.itemCount}',
                    Icons.shopping_basket_outlined,
                  ),
                ),

                Container(
                  width: 1,
                  height: 30,
                  color: Colors.grey.shade300,
                ),

                // Total Quantity
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'الكمية',
                    '${cart.totalQuantity}',
                    Icons.numbers,
                  ),
                ),

                Container(
                  width: 1,
                  height: 30,
                  color: Colors.grey.shade300,
                ),

                // Total Price
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'المجموع',
                    '${cart.totalPrice.toStringAsFixed(2)} ج.م',
                    Icons.monetization_on_outlined,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'Gilroy',
                color: Colors.grey.shade600,
              ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'Gilroy',
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
