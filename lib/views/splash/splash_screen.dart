import 'package:flutter/material.dart';
import 'package:grocery/api/api.dart';
import '../../core/constants/constants.dart';
import '../../services/auth.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/onboarding/onboarding_page.dart';
import '../../views/entrypoint/entrypoint_ui.dart';
import '../../views/entrypoint/wholesaler_entrypoint_ui.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthenticationAndRedirect();
  }

  Future<void> _checkAuthenticationAndRedirect() async {
    // Add a small delay to show the splash screen briefly
    await Future.delayed(const Duration(milliseconds: 1500));

    if (!mounted) return;

    try {
      // Check if user is logged in
      final isLoggedIn = await AuthService.isLoggedIn();

      final token = await AuthService.getToken();
      if (token != null) {
        HttpService.instance.setToken(token);
      }

      if (!mounted) return;

      if (isLoggedIn) {
        // User is logged in, check user type for appropriate redirect
        final isWholesaler = await AuthService.isWholesaler();

        if (!mounted) return;

        if (isWholesaler) {
          // Redirect to wholesaler dashboard
          nav.Router.pushReplacement(context, const WholesalerEntryPointUI());
        } else {
          // Redirect to main app entry point
          nav.Router.pushReplacement(context, const EntryPointUI());
        }
      } else {
        // User is not logged in, redirect to onboarding
        nav.Router.pushReplacement(context, const OnboardingPage());
      }
    } catch (e) {
      // In case of any error, default to onboarding
      if (mounted) {
        nav.Router.pushReplacement(context, const OnboardingPage());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      spreadRadius: 5,
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.asset(
                    'assets/images/app_logo.png',
                    width: 80,
                    height: 80,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // App Name
              Text(
                'تاجر بلس',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
              ),

              const SizedBox(height: 10),

              // Tagline
              Text(
                'شريكك في البقالة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w300,
                    ),
              ),

              const SizedBox(height: 50),

              // Loading Indicator
              const SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              ),

              const SizedBox(height: 20),

              // Loading Text
              Text(
                'جارِ التحميل...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
