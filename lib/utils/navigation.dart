import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class Router {
  /// Navigate to a new page
  static void push(BuildContext context, Widget widget) {
    Navigator.push(context, CupertinoPageRoute(builder: (context) => widget));
  }

  /// Navigate to a new page and replace the current one
  static void pushReplacement(BuildContext context, Widget widget) {
    Navigator.pushReplacement(
        context, CupertinoPageRoute(builder: (context) => widget));
  }

  /// Navigate to a new page and remove all previous routes
  static void pushAndRemoveUntil(BuildContext context, Widget widget) {
    Navigator.pushAndRemoveUntil(context,
        CupertinoPageRoute(builder: (context) => widget), (route) => false);
  }

  /// Navigate to a new page and remove routes until a specific condition
  static void pushAndRemoveUntilFirst(BuildContext context, Widget widget) {
    Navigator.pushAndRemoveUntil(
        context,
        CupertinoPageRoute(builder: (context) => widget),
        (route) => route.isFirst);
  }

  /// Navigate to a new page with result expectation
  static Future<T?> pushForResult<T>(BuildContext context, Widget widget) {
    return Navigator.push<T>(
        context, CupertinoPageRoute(builder: (context) => widget));
  }

  /// Show a dialog
  static void dialog(BuildContext context, Widget widget) {
    showDialog(context: context, builder: (context) => widget);
  }

  /// Show a snackbar
  static void snackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(message)));
  }

  /// Pop the current route
  static void pop(BuildContext context, [dynamic result]) {
    Navigator.pop(context, result);
  }

  /// Pop routes until the first route
  static void popToFirst(BuildContext context) {
    Navigator.popUntil(context, (route) => route.isFirst);
  }

  /// Check if can pop
  static bool canPop(BuildContext context) {
    return Navigator.canPop(context);
  }
}
