import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/constants/app_icons.dart';
import '../../core/components/app_settings_tile.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/drawer/about_us_page.dart';
import '../../views/drawer/faq_page.dart';
import '../../views/drawer/terms_and_conditions_page.dart';
import '../../views/drawer/help_page.dart';
import '../../views/drawer/contact_us_page.dart';
import '../../views/auth/intro_login_page.dart';

class DrawerPage extends StatelessWidget {
  const DrawerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text('القائمة'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          children: [
            AppSettingsListTile(
              label: 'دعوة صديق',
              trailing: SvgPicture.asset(AppIcons.right),
            ),
            AppSettingsListTile(
              label: 'من نحن',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const AboutUsPage()),
            ),
            AppSettingsListTile(
              label: 'الأسئلة الشائعة',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const FAQPage()),
            ),
            AppSettingsListTile(
              label: 'الشروط والأحكام',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () =>
                  nav.Router.push(context, const TermsAndConditionsPage()),
            ),
            AppSettingsListTile(
              label: 'مركز المساعدة',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const HelpPage()),
            ),
            AppSettingsListTile(
              label: 'قيم التطبيق',
              trailing: SvgPicture.asset(AppIcons.right),
              // onTap: () => Navigator.pushNamed(context, AppRoutes.help),
            ),
            AppSettingsListTile(
              label: 'سياسة الخصوصية',
              trailing: SvgPicture.asset(AppIcons.right),
              // onTap: () => Navigator.pushNamed(context, AppRoutes.),
            ),
            AppSettingsListTile(
              label: 'تواصل معنا',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const ContactUsPage()),
            ),
            const SizedBox(height: AppDefaults.padding * 3),
            AppSettingsListTile(
              label: 'تسجيل الخروج',
              trailing: SvgPicture.asset(AppIcons.right),
              onTap: () => nav.Router.push(context, const IntroLoginPage()),
            ),
          ],
        ),
      ),
    );
  }
}
