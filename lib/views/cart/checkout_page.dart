import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../services/app_services.dart';
import '../../services/cart_service.dart';
import '../../api/orders_api.dart';
import '../../models/store_models.dart';
import '../../models/cart_models.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import 'components/delivery_time_selector.dart';
import 'components/store_selector.dart';
import 'components/cart_totals_card.dart';

class CheckoutPage extends StatefulWidget {
  const CheckoutPage({super.key});

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  DateTime? _selectedDeliveryDate;
  StoreData? _selectedStore;
  bool _isCreatingOrder = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _selectedDeliveryDate =
        DateTime.now().add(const Duration(days: 1)); // Default to tomorrow
  }

  bool get _canProceedToOrder {
    return _selectedDeliveryDate != null && _selectedStore != null;
  }

  Future<void> _createOrder() async {
    if (!_canProceedToOrder) {
      _showError('يجب اختيار موعد التوصيل والمتجر');
      return;
    }

    final cartService = AppServices().cartService;
    final regionService = AppServices().regionService;

    if (!cartService.hasCart) {
      _showError('السلة فارغة');
      return;
    }

    setState(() {
      _isCreatingOrder = true;
      _error = null;
    });

    try {
      // Validate cart before creating order
      final cartValidation = await cartService.validateCartForOrder();

      if (!cartValidation.isValid) {
        _showCartValidationErrors(cartValidation);
        return;
      }

      // Prepare order items from cart
      final orderItems = cartService.cart!.items.map((cartItem) {
        return OrderItemRequest(
          itemId: cartItem.itemId,
          quantity: cartItem.quantity,
        );
      }).toList();

      // Create order request
      final orderRequest = CreateOrderRequest(
        wholesalerId: cartService.currentWholesalerId!,
        storeId: _selectedStore!.id,
        items: orderItems,
        deliverAt: _selectedDeliveryDate!.toIso8601String(),
      );

      // Create the order
      final orderResponse = await OrdersApiService.createOrder(orderRequest);

      // Clear the cart after successful order creation
      await cartService.clearCart();

      if (mounted) {
        // Navigate to order success page
        nav.Router.pushAndRemoveUntilFirst(
            context, const OrderSuccessfullPage());
      }
    } catch (e) {
      if (e is DioException) {
        String errorMessage =
            e.response?.data['detail'] ?? 'حدث خطأ في إنشاء الطلب';

        // Check if it's a quantity constraint error from backend
        if (errorMessage.contains('الكمية المطلوبة') ||
            errorMessage.contains('أقل من الحد الأدنى') ||
            errorMessage.contains('أكبر من الحد الأقصى')) {
          // This is a quantity validation error, show it directly
          _showError(errorMessage);
        } else {
          _showError(errorMessage);
        }
      } else {
        _showError('حدث خطأ في إنشاء الطلب: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreatingOrder = false;
        });
      }
    }
  }

  void _showError(String message) {
    setState(() {
      _error = message;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Gilroy'),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showCartValidationErrors(CartValidationResult validation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مشاكل في السلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(validation.errorMessage ?? 'توجد مشاكل في بعض المنتجات'),
            const SizedBox(height: 16),
            ...validation.invalidItems.map((error) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        _getErrorIcon(error.errorType),
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              error.cartItem.productName,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              error.errorMessage,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to cart to fix issues
            },
            child: const Text('العودة للسلة'),
          ),
        ],
      ),
    );
  }

  IconData _getErrorIcon(CartValidationErrorType errorType) {
    switch (errorType) {
      case CartValidationErrorType.quantityConstraint:
        return Icons.warning;
      case CartValidationErrorType.insufficientInventory:
        return Icons.inventory_2;
      case CartValidationErrorType.itemNotFound:
        return Icons.error;
    }
  }

  int? _getRequiredRegionId() {
    final cartService = AppServices().cartService;
    final regionService = AppServices().regionService;

    // For now, we'll use the selected region ID
    // In a more complex setup, you'd get this from the wholesaler's region requirements
    return regionService.selectedRegion?.id;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text(
          'إكمال الطلب',
          style: TextStyle(
            fontFamily: 'Gilroy',
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Consumer<CartService>(
        builder: (context, cartService, child) {
          if (!cartService.hasCart) {
            return _buildEmptyCartWidget();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Delivery Time Selection
                DeliveryTimeSelector(
                  selectedDate: _selectedDeliveryDate,
                  onDateSelected: (date) {
                    setState(() {
                      _selectedDeliveryDate = date;
                    });
                  },
                ),

                // Store Selection
                StoreSelector(
                  selectedStore: _selectedStore,
                  onStoreSelected: (store) {
                    setState(() {
                      _selectedStore = store;
                    });
                  },
                  requiredRegionId: _getRequiredRegionId(),
                ),

                // Order Summary
                CartTotalsCard(
                  subtotal: cartService.totalPrice,
                  total: cartService.totalPrice,
                  itemCount: cartService.totalQuantity,
                ),

                // Error Display
                if (_error != null) ...[
                  Container(
                    margin: const EdgeInsets.all(AppDefaults.padding),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _error!,
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontFamily: 'Gilroy',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Order Button
                Container(
                  margin: const EdgeInsets.all(AppDefaults.padding),
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _canProceedToOrder && !_isCreatingOrder
                        ? _createOrder
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: _canProceedToOrder
                          ? Theme.of(context).primaryColor
                          : Colors.grey,
                    ),
                    child: _isCreatingOrder
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            _canProceedToOrder
                                ? 'تأكيد الطلب'
                                : 'يجب اختيار موعد التوصيل والمتجر',
                            style: const TextStyle(
                              fontFamily: 'Gilroy',
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyCartWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'السلة فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontFamily: 'Gilroy',
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 12),
            Text(
              'لا يمكن إكمال الطلب بدون منتجات في السلة',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontFamily: 'Gilroy',
                    color: Colors.grey.shade500,
                  ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'العودة للتسوق',
                style: TextStyle(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
