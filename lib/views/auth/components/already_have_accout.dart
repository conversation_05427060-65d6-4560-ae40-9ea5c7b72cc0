import 'package:flutter/material.dart';

import '../../../utils/navigation.dart' as nav;
import '../../../views/auth/login_page.dart';

class AlreadyHaveAnAccount extends StatelessWidget {
  const AlreadyHaveAnAccount({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('لديك حساب بالفعل؟'),
        TextButton(
          onPressed: () => nav.Router.push(context, const LoginPage()),
          child: const Text('تسجيل الدخول'),
        ),
      ],
    );
  }
}
