<tr class="item-row" data-item-id="{{ item.id }}">
    <td>
        <input type="checkbox" data-item-id="{{ item.id }}" onchange="selectItem({{ item.id }})" class="form-check-input">
    </td>
    
    <td>
        <div class="d-flex align-items-center">
            {% if item.product.image %}
            <img src="{{ item.product.image.url }}" 
                 alt="{{ item.product.name }}" 
                 class="rounded me-3" 
                 style="width: 50px; height: 50px; object-fit: cover;">
            {% else %}
            <div class="bg-light rounded d-flex align-items-center justify-content-center me-3" 
                 style="width: 50px; height: 50px;">
                <i class="fas fa-image text-muted"></i>
            </div>
            {% endif %}
            <div>
                <div class="fw-semibold">{{ item.product.name }}</div>
                <small class="text-muted">{{ item.product.title }}</small>
                {% if item.product.barcode %}
                <small class="text-muted d-block">{{ item.product.barcode }}</small>
                {% endif %}
            </div>
        </div>
    </td>
    
    <td>
        {% if item.product.company %}
        <span class="badge bg-light text-dark">{{ item.product.company.name }}</span>
        {% else %}
        <span class="text-muted">غير محدد</span>
        {% endif %}
    </td>
    
    <td>
        {% if item.product.category %}
        <span class="badge bg-secondary">{{ item.product.category.name }}</span>
        {% else %}
        <span class="text-muted">غير محدد</span>
        {% endif %}
    </td>
    
    <td>
        <div class="input-group input-group-sm" style="width: 120px;">
            <input type="number" 
                   class="form-control" 
                   value="{{ item.base_price }}" 
                   step="0.01"
                   onchange="debounceQuickEdit({{ item.id }}, 'base_price', this.value)"
                   title="تعديل السعر">
            <span class="input-group-text">ج.م</span>
        </div>
    </td>
    
    <td>
        <div class="d-flex align-items-center">
            <input type="number" 
                   class="form-control form-control-sm me-2" 
                   value="{{ item.inventory_count }}" 
                   style="width: 80px;"
                   onchange="debounceQuickEdit({{ item.id }}, 'inventory_count', this.value)"
                   title="تعديل المخزون">
            {% if item.inventory_count == 0 %}
                <span class="badge bg-danger">نفد</span>
            {% elif item.inventory_count < 10 %}
                <span class="badge bg-warning">منخفض</span>
            {% else %}
                <span class="badge bg-success">متوفر</span>
            {% endif %}
        </div>
    </td>
    
    <td>
        <input type="number" 
               class="form-control form-control-sm" 
               value="{{ item.minimum_order_quantity }}" 
               style="width: 80px;"
               onchange="debounceQuickEdit({{ item.id }}, 'minimum_order_quantity', this.value)"
               title="الحد الأدنى للطلب">
    </td>
    
    <td>
        <input type="number" 
               class="form-control form-control-sm" 
               value="{{ item.maximum_order_quantity|default:'' }}" 
               style="width: 80px;"
               onchange="debounceQuickEdit({{ item.id }}, 'maximum_order_quantity', this.value)"
               title="الحد الأقصى للطلب"
               placeholder="غير محدد">
    </td>
    
    <td>
        <div class="btn-group btn-group-sm">
            <!-- View Details Button -->
            <a href="{% url 'item_detail' item.id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </a>
            
            <!-- Edit Button -->
            <a href="{% url 'item_edit' item.id %}" class="btn btn-outline-success" title="تعديل">
                <i class="fas fa-edit"></i>
            </a>
            
            <!-- Quick Actions Dropdown -->
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="#" onclick="duplicateItem({{ item.id }})">
                            <i class="fas fa-copy me-2"></i>
                            نسخ المنتج
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="viewHistory({{ item.id }})">
                            <i class="fas fa-history me-2"></i>
                            تاريخ التعديلات
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="deleteItem({{ item.id }})">
                            <i class="fas fa-trash me-2"></i>
                            حذف المنتج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </td>
</tr>

<style>
.item-row:hover {
    background-color: var(--light-green) !important;
}

.item-row input[type="number"] {
    border: 1px solid transparent;
    background: transparent;
    transition: all 0.3s ease;
}

.item-row input[type="number"]:hover,
.item-row input[type="number"]:focus {
    border-color: var(--primary-green);
    background: white;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.item-row .input-group input[type="number"] {
    border-right: 1px solid #dee2e6;
}

.item-row .input-group input[type="number"]:hover,
.item-row .input-group input[type="number"]:focus {
    border-color: var(--primary-green);
}

/* Loading state for inputs */
.item-row input.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Success state for inputs */
.item-row input.success {
    border-color: var(--primary-green);
    background-color: rgba(40, 167, 69, 0.1);
}

/* Error state for inputs */
.item-row input.error {
    border-color: var(--danger-red);
    background-color: rgba(220, 53, 69, 0.1);
}
</style>

<script>
function duplicateItem(itemId) {
    if (confirm('هل تريد نسخ هذا المنتج؟')) {
        fetch(`/wholesaler/items/${itemId}/duplicate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء نسخ المنتج');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء نسخ المنتج');
        });
    }
}

function viewHistory(itemId) {
    // Open history modal or navigate to history page
    window.open(`/wholesaler/items/${itemId}/history/`, '_blank');
}

function deleteItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/wholesaler/items/${itemId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row with animation
                const row = document.querySelector(`[data-item-id="${itemId}"]`).closest('tr');
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                }, 300);
            } else {
                alert('حدث خطأ أثناء حذف المنتج');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف المنتج');
        });
    }
}
</script>
