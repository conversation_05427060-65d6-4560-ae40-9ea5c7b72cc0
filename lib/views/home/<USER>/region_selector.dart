import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';
import '../../../services/app_services.dart';
import '../dialogs/region_selection_dialog.dart';

class RegionSelector extends StatefulWidget {
  const RegionSelector({super.key});

  @override
  State<RegionSelector> createState() => _RegionSelectorState();
}

class _RegionSelectorState extends State<RegionSelector> {
  late final _regionService = AppServices().regionService;

  @override
  void initState() {
    super.initState();
    _regionService.addListener(_onRegionServiceChanged);
    _initializeRegionService();
  }

  void _onRegionServiceChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _initializeRegionService() async {
    // Service is already initialized globally, just check if region selection is needed
    if (_regionService.isRegionSelectionRequired && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showRegionSelectionDialog();
      });
    }
  }

  Future<void> _showRegionSelectionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Don't allow closing without selection
      builder: (context) => const RegionSelectionDialog(),
    );

    if (result == true && mounted) {
      setState(() {}); // Refresh the display
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDefaults.padding,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.location_on,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        title: Text(
          'المنطقة الحالية',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        subtitle: Text(
          _regionService.currentRegionDisplayName,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_regionService.isLoading)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              const Icon(
                Icons.edit_location_alt,
                color: AppColors.primary,
                size: 20,
              ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
        onTap: _regionService.isLoading ? null : _showRegionSelectionDialog,
      ),
    );
  }

  @override
  void dispose() {
    _regionService.removeListener(_onRegionServiceChanged);
    super.dispose();
  }
}
