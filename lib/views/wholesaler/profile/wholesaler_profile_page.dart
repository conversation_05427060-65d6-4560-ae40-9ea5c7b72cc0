import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../services/auth.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/auth/login_or_signup_page.dart';
import 'repository/profile_repository.dart';
import 'models/profile_models.dart';
import 'components/profile_header.dart';
import 'components/profile_info_section.dart';
import 'components/profile_actions_section.dart';
import 'components/profile_edit_dialog.dart';

class WholesalerProfilePage extends StatefulWidget {
  const WholesalerProfilePage({super.key});

  @override
  State<WholesalerProfilePage> createState() => _WholesalerProfilePageState();
}

class _WholesalerProfilePageState extends State<WholesalerProfilePage> {
  late final ProfileRepository _repository;

  @override
  void initState() {
    super.initState();
    _repository = ProfileRepository();
    _repository.addListener(_onRepositoryChanged);
    _loadProfile();
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadProfile() async {
    await _repository.loadProfile(forceRefresh: true);
  }

  Future<void> _handleRefresh() async {
    await _repository.refreshAll();
  }

  void _showEditDialog() {
    if (_repository.profileData == null) return;

    showDialog(
      context: context,
      builder: (context) => ProfileEditDialog(
        profileData: _repository.profileData!,
        onSave: _handleProfileUpdate,
      ),
    );
  }

  Future<void> _handleProfileUpdate(ProfileUpdateRequest request) async {
    final success = await _repository.updateProfile(request);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الملف الشخصي بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(_repository.updateError ?? 'فشل في تحديث الملف الشخصي'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _logout() async {
    final confirmed = await _showLogoutConfirmation();
    if (confirmed) {
      await AuthService.logout();
      _repository.clearData();
      if (mounted) {
        nav.Router.pushAndRemoveUntil(context, const LoginOrSignUpPage());
      }
    }
  }

  Future<bool> _showLogoutConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        automaticallyImplyLeading: false,
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_repository.isLoadingProfile) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_repository.profileError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _repository.profileError!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadProfile,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (!_repository.hasData) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    final profileData = _repository.profileData!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Column(
        children: [
          // Profile Header
          ProfileHeader(
            profileData: profileData,
            onImageUpload: _handleImageUpload,
            isUploading: _repository.isUploadingImage,
          ),
          const SizedBox(height: AppDefaults.padding),

          // Profile Information
          ProfileInfoSection(profileData: profileData),
          const SizedBox(height: AppDefaults.padding),

          // Profile Actions
          ProfileActionsSection(
            onEditProfile: _showEditDialog,
            onLogout: _logout,
            isUpdating: _repository.isUpdatingProfile,
          ),
        ],
      ),
    );
  }

  Future<void> _handleImageUpload(String filePath, bool isBackground) async {
    bool success;
    if (isBackground) {
      success = await _repository.uploadBackgroundImage(filePath);
    } else {
      success = await _repository.uploadProfileImage(filePath);
    }

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفع الصورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_repository.uploadError ?? 'فشل في رفع الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
