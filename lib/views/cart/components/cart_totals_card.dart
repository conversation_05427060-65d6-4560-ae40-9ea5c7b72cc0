import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';

class CartTotalsCard extends StatelessWidget {
  final double subtotal;
  final double total;
  final int itemCount;
  final double deliveryFee;
  final double discount;

  const CartTotalsCard({
    super.key,
    required this.subtotal,
    required this.total,
    required this.itemCount,
    this.deliveryFee = 0.0,
    this.discount = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'ملخص الطلب',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 16),

          // Item Count
          _buildTotalRow(
            context,
            'عدد المنتجات',
            '$itemCount منتج',
            isMainRow: false,
          ),

          const SizedBox(height: 8),

          // Subtotal
          _buildTotalRow(
            context,
            'المجموع الفرعي',
            '${subtotal.toStringAsFixed(2)} ج.م',
            isMainRow: false,
          ),

          // Delivery Fee (if applicable)
          if (deliveryFee > 0) ...[
            const SizedBox(height: 8),
            _buildTotalRow(
              context,
              'رسوم التوصيل',
              '${deliveryFee.toStringAsFixed(2)} ج.م',
              isMainRow: false,
            ),
          ],

          // Discount (if applicable)
          if (discount > 0) ...[
            const SizedBox(height: 8),
            _buildTotalRow(
              context,
              'الخصم',
              '-${discount.toStringAsFixed(2)} ج.م',
              isMainRow: false,
              valueColor: Colors.green.shade600,
            ),
          ],

          const SizedBox(height: 12),
          const Divider(),
          const SizedBox(height: 12),

          // Total
          _buildTotalRow(
            context,
            'الإجمالي',
            '${total.toStringAsFixed(2)} ج.م',
            isMainRow: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(
    BuildContext context,
    String label,
    String value, {
    bool isMainRow = false,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'Gilroy',
                fontWeight: isMainRow ? FontWeight.bold : FontWeight.w500,
                color: isMainRow ? Colors.black : Colors.grey.shade700,
                fontSize: isMainRow ? 16 : 14,
              ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'Gilroy',
                fontWeight: isMainRow ? FontWeight.bold : FontWeight.w600,
                color: valueColor ??
                    (isMainRow
                        ? Theme.of(context).primaryColor
                        : Colors.black87),
                fontSize: isMainRow ? 16 : 14,
              ),
        ),
      ],
    );
  }
}
