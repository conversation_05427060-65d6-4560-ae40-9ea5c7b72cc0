import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/profile/order/my_order_page.dart';
import '../../../views/profile/stores/stores_list_page.dart';
import 'profile_squre_tile.dart';

class ProfileHeaderOptions extends StatelessWidget {
  const ProfileHeaderOptions({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          ProfileSqureTile(
            label: 'كل الطلبات',
            icon: AppIcons.truckIcon,
            onTap: () {
              nav.Router.push(context, const AllOrderPage());
            },
          ),
          // ProfileSqureTile(
          //   label: 'قسيمة',
          //   icon: AppIcons.voucher,
          //   onTap: () {
          //     Navigator.pushNamed(context, AppRoutes.coupon);
          //   },
          // ),
          // ProfileSqureTile(
          //   label: 'العنوان',
          //   icon: AppIcons.homeProfile,
          //   onTap: () {
          //     Navigator.pushNamed(context, AppRoutes.deliveryAddress);
          //   },
          // ),
          ProfileSqureTile(
            label: 'المتاجر',
            icon: AppIcons.homeProfile,
            onTap: () {
              nav.Router.push(context, const StoresListPage());
            },
          ),
        ],
      ),
    );
  }
}
