import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:grocery/core/routes/app_routes.dart';

import '../../core/components/product_tile_square.dart';
import '../../core/constants/constants.dart';
import '../../api/home/<USER>';
import '../../api/home/<USER>';

class SearchResultPage extends StatefulWidget {
  const SearchResultPage({
    super.key,
    this.initialQuery,
    this.initialResults,
  });

  final String? initialQuery;
  final List<ProductWithPricing>? initialResults;

  @override
  State<SearchResultPage> createState() => _SearchResultPageState();
}

class _SearchResultPageState extends State<SearchResultPage> {
  final TextEditingController _searchController = TextEditingController();
  List<ProductWithPricing> _searchResults = [];
  bool _isLoading = false;
  String? _error;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleInitialArguments();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _handleInitialArguments() {
    // First try to get from constructor parameters
    String? query = widget.initialQuery;
    List<ProductWithPricing>? results = widget.initialResults;

    // Fallback to route arguments for backward compatibility
    if (query == null) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        query = args['query'] as String?;
        results = args['results'] as List<ProductWithPricing>?;
      }
    }

    if (query != null) {
      _currentQuery = query;
      _searchController.text = query;

      if (results != null) {
        setState(() {
            _searchResults = results!;
          });
        } else {
          _performSearch(query);
        }
      }
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentQuery = query.trim();
    });

    try {
      final results = await HomeApiService.searchProducts(query.trim());
      setState(() {
        _searchResults = results;
        _isLoading = false;
        _error = null; // Clear any previous errors
      });
    } catch (e) {
      setState(() {
        _error = _getErrorMessage(e);
        _isLoading = false;
        _searchResults = []; // Clear results on error
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.drawerPage);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2F6F3),
              shape: const CircleBorder(),
            ),
            child: SvgPicture.asset(AppIcons.sidebarIcon),
          ),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AppIcons.logoCropped,
              height: 32,
            ),
            const SizedBox(width: 11),
            Text(
              "تاجر بلس",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن المنتجات...',
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  child: SvgPicture.asset(AppIcons.search),
                ),
                suffixIconConstraints: const BoxConstraints(),
              ),
              textInputAction: TextInputAction.search,
              onChanged: (value) {
                // Optional: Add debounced search here if desired
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _performSearch(value.trim());
                }
              },
            ),
          ),

          // Content based on current state
          _buildContent(),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري البحث...',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _performSearch(_currentQuery);
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // Show search suggestions when no search has been performed
    if (_currentQuery.isEmpty && _searchResults.isEmpty && !_isLoading) {
      return _buildSearchSuggestions();
    }

    if (_searchResults.isEmpty && _currentQuery.isNotEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لم يتم العثور على نتائج',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        if (_searchResults.isNotEmpty) ...[
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              child: Text(
                'تم العثور على ${_searchResults.length} منتج',
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(color: Colors.black),
              ),
            ),
          ),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(top: AppDefaults.padding),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              childAspectRatio: 0.66,
            ),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final product = _searchResults[index];
              return ProductTileSquare(
                data: product.toProductModel(),
                productId: product.id,
              );
            },
          ),
        ],
      ],
    );
  }

  /// Convert exception to user-friendly Arabic error message
  String _getErrorMessage(dynamic error) {
    if (error is HomeApiException) {
      // Handle specific API errors
      final message = error.message.toLowerCase();

      if (message.contains('timeout') || message.contains('connection')) {
        return 'انتهت مهلة الاتصال. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.';
      } else if (message.contains('network') || message.contains('internet')) {
        return 'لا يوجد اتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
      } else if (message.contains('authentication') ||
          message.contains('401')) {
        return 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.';
      } else if (message.contains('server') || message.contains('500')) {
        return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
      } else if (message.contains('not found') || message.contains('404')) {
        return 'لم يتم العثور على المنتجات المطلوبة.';
      } else {
        return 'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.';
      }
    } else {
      // Handle generic errors
      return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }

  Widget _buildSearchSuggestions() {
    final searchSuggestions = [
      'خضروات',
      'فواكه',
      'لحوم',
      'أسماك',
      'منتجات ألبان',
      'خبز',
      'حلويات',
      'مشروبات',
      'توابل',
      'أرز',
      'مكرونة',
      'زيوت',
      'معلبات',
      'مجمدات',
      'وجبات خفيفة',
      'مواد تنظيف',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
          child: Text(
            'اقتراحات البحث',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 16),
          itemBuilder: (context, index) {
            final suggestion = searchSuggestions[index];
            return _SearchSuggestionTile(
              searchTerm: suggestion,
              onTap: () {
                _searchController.text = suggestion;
                _performSearch(suggestion);
              },
            );
          },
          separatorBuilder: (context, index) => const Divider(
            thickness: 0.1,
          ),
          itemCount: searchSuggestions.length,
        ),
      ],
    );
  }
}

class _SearchSuggestionTile extends StatelessWidget {
  const _SearchSuggestionTile({
    required this.searchTerm,
    required this.onTap,
  });

  final String searchTerm;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        child: Row(
          children: [
            Text(
              searchTerm,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Spacer(),
            SvgPicture.asset(AppIcons.searchTileArrow),
          ],
        ),
      ),
    );
  }
}
